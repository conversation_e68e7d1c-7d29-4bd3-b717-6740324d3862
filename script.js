// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Toggle mobile menu
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const isClickInsideNav = navMenu.contains(event.target) || hamburger.contains(event.target);
        if (!isClickInsideNav && navMenu.classList.contains('active')) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }
    });
});

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            const offsetTop = target.offsetTop - 70; // Account for fixed navbar
            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        }
    });
});

// Navbar background change on scroll
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = 'none';
    }
});

// Active navigation link highlighting
window.addEventListener('scroll', function() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');

    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.clientHeight;
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            current = section.getAttribute('id');
        }
    });

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animatedElements = document.querySelectorAll('.capability-card, .principle-card, .about-card, .example-card');

    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Copy to clipboard functionality for code examples
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        showNotification('Copied to clipboard!', 'success');
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        showNotification('Failed to copy to clipboard', 'error');
    });
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 8px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        ${type === 'success' ? 'background-color: #10b981;' : ''}
        ${type === 'error' ? 'background-color: #ef4444;' : ''}
        ${type === 'info' ? 'background-color: #3b82f6;' : ''}
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Enhanced scroll animations
function initScrollAnimations() {
    const cards = document.querySelectorAll('.capability-card, .principle-card, .about-card');

    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
}

// Initialize scroll animations when DOM is loaded
document.addEventListener('DOMContentLoaded', initScrollAnimations);

// Parallax effect for hero section
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    const heroContent = document.querySelector('.hero-content');

    if (hero && scrolled < hero.offsetHeight) {
        heroContent.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
});

// Tech tag hover effects
document.addEventListener('DOMContentLoaded', function() {
    const techTags = document.querySelectorAll('.tech-tag');

    techTags.forEach(tag => {
        tag.addEventListener('mouseenter', function() {
            this.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            this.style.color = 'white';
            this.style.transform = 'translateY(-2px)';
        });

        tag.addEventListener('mouseleave', function() {
            this.style.background = '#e5e7eb';
            this.style.color = '#374151';
            this.style.transform = 'translateY(0)';
        });
    });
});

// Loading screen (optional)
window.addEventListener('load', function() {
    const loader = document.querySelector('.loader');
    if (loader) {
        loader.style.opacity = '0';
        setTimeout(() => {
            loader.style.display = 'none';
        }, 500);
    }
});

// Keyboard navigation support
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const navMenu = document.querySelector('.nav-menu');
        const hamburger = document.querySelector('.hamburger');
        if (navMenu.classList.contains('active')) {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        }
    }
});

// Performance optimization: Throttle scroll events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
const throttledScrollHandler = throttle(function() {
    // Navbar background change
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 50) {
        navbar.style.background = 'rgba(255, 255, 255, 0.98)';
        navbar.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
    } else {
        navbar.style.background = 'rgba(255, 255, 255, 0.95)';
        navbar.style.boxShadow = 'none';
    }

    // Parallax effect
    const scrolled = window.pageYOffset;
    const hero = document.querySelector('.hero');
    const heroContent = document.querySelector('.hero-content');

    if (hero && scrolled < hero.offsetHeight) {
        heroContent.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
}, 16); // ~60fps

window.addEventListener('scroll', throttledScrollHandler);

// Interactive Demo Functionality
class ManusAIDemo {
    constructor() {
        this.chatMessages = document.getElementById('chat-messages');
        this.chatInput = document.getElementById('chat-input');
        this.sendBtn = document.getElementById('send-btn');
        this.quickPrompts = document.querySelectorAll('.quick-prompt');
        this.tryDemoBtn = document.getElementById('try-demo-btn');

        this.responses = {
            'help me write a professional email': {
                response: `I'd be happy to help you write a professional email! Here's a template structure:

**Subject Line:** Clear and specific
**Greeting:** Dear [Name] / Hello [Name]
**Opening:** Brief context or reference
**Body:** Main message with clear points
**Closing:** Next steps or call to action
**Sign-off:** Best regards, [Your name]

Would you like me to help you write a specific email? Just tell me:
- Who you're writing to
- What the email is about
- The tone you want (formal, friendly, etc.)`,
                type: 'content'
            },
            'explain machine learning in simple terms': {
                response: `Machine Learning is like teaching a computer to recognize patterns, just like how you learned to recognize faces!

🧠 **Think of it this way:**
- You show a computer thousands of photos labeled "cat" or "dog"
- The computer finds patterns (cats have pointy ears, dogs vary more in size)
- Now it can guess if a new photo shows a cat or dog

🔍 **Three main types:**
1. **Supervised Learning:** Learning with examples (like the cat/dog photos)
2. **Unsupervised Learning:** Finding hidden patterns in data
3. **Reinforcement Learning:** Learning through trial and error (like playing games)

🚀 **Real applications:**
- Netflix recommendations
- Voice assistants
- Medical diagnosis
- Self-driving cars

It's essentially pattern recognition at scale!`,
                type: 'explanation'
            },
            'create a python script to analyze data': {
                response: `Here's a Python script template for data analysis:

\`\`\`python
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Load your data
df = pd.read_csv('your_data.csv')

# Basic exploration
print("Dataset shape:", df.shape)
print("\\nFirst 5 rows:")
print(df.head())

print("\\nData types:")
print(df.dtypes)

print("\\nMissing values:")
print(df.isnull().sum())

# Statistical summary
print("\\nStatistical summary:")
print(df.describe())

# Visualizations
plt.figure(figsize=(12, 8))

# Correlation heatmap
plt.subplot(2, 2, 1)
sns.heatmap(df.corr(), annot=True, cmap='coolwarm')
plt.title('Correlation Matrix')

# Distribution plot
plt.subplot(2, 2, 2)
df['your_column'].hist(bins=30)
plt.title('Distribution')

plt.tight_layout()
plt.show()
\`\`\`

What specific type of data analysis do you need help with?`,
                type: 'code'
            },
            'research the latest ai trends': {
                response: `Here are the hottest AI trends in 2024:

🚀 **Generative AI Evolution**
- Multimodal AI (text, image, video, audio)
- Real-time AI assistants
- AI-powered content creation tools

🧠 **Large Language Models (LLMs)**
- More efficient, smaller models
- Specialized domain-specific models
- Better reasoning capabilities

🔧 **AI Integration**
- AI-first software development
- No-code AI platforms
- AI in everyday applications

🎯 **Emerging Applications**
- AI in healthcare diagnostics
- Autonomous systems
- AI-powered education
- Creative AI tools

⚡ **Technical Advances**
- Edge AI computing
- Quantum-AI hybrid systems
- Neuromorphic computing

🛡️ **AI Safety & Ethics**
- Responsible AI development
- AI governance frameworks
- Bias detection and mitigation

Would you like me to dive deeper into any of these trends?`,
                type: 'research'
            }
        };

        this.init();
    }

    init() {
        // Enable send button when input has content
        this.chatInput.addEventListener('input', () => {
            this.sendBtn.disabled = this.chatInput.value.trim() === '';
        });

        // Send message on button click
        this.sendBtn.addEventListener('click', () => {
            this.sendMessage();
        });

        // Send message on Enter key
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !this.sendBtn.disabled) {
                this.sendMessage();
            }
        });

        // Quick prompt buttons
        this.quickPrompts.forEach(btn => {
            btn.addEventListener('click', () => {
                const prompt = btn.dataset.prompt;
                this.chatInput.value = prompt;
                this.sendBtn.disabled = false;
                this.sendMessage();
            });
        });

        // Try demo button
        if (this.tryDemoBtn) {
            this.tryDemoBtn.addEventListener('click', () => {
                document.getElementById('demo').scrollIntoView({ behavior: 'smooth' });
            });
        }
    }

    sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;

        // Add user message
        this.addMessage(message, 'user');

        // Clear input
        this.chatInput.value = '';
        this.sendBtn.disabled = true;

        // Show typing indicator
        this.showTypingIndicator();

        // Simulate AI response delay
        setTimeout(() => {
            this.hideTypingIndicator();
            this.generateResponse(message);
        }, 1500 + Math.random() * 1000);
    }

    addMessage(content, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}-message`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';

        if (content.includes('```')) {
            // Handle code blocks
            messageContent.innerHTML = this.formatCodeBlocks(content);
        } else {
            messageContent.innerHTML = this.formatMessage(content);
        }

        messageDiv.appendChild(avatar);
        messageDiv.appendChild(messageContent);

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    formatMessage(content) {
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>')
            .replace(/🚀|🧠|🔍|🎯|⚡|🛡️|🔧/g, '<span style="font-size: 1.2em;">$&</span>');
    }

    formatCodeBlocks(content) {
        return content.replace(/```(\w+)?\n([\s\S]*?)```/g,
            '<pre style="background: #f8f9fa; padding: 1rem; border-radius: 8px; overflow-x: auto; margin: 0.5rem 0;"><code>$2</code></pre>'
        );
    }

    generateResponse(userMessage) {
        const lowerMessage = userMessage.toLowerCase();
        let response = null;

        // Find matching response
        for (const [key, value] of Object.entries(this.responses)) {
            if (lowerMessage.includes(key.toLowerCase()) ||
                key.toLowerCase().includes(lowerMessage)) {
                response = value;
                break;
            }
        }

        // Default responses for common patterns
        if (!response) {
            if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
                response = {
                    response: "Hello! I'm Manus AI, your intelligent assistant. I can help you with research, writing, coding, data analysis, and much more. What would you like to work on today?",
                    type: 'greeting'
                };
            } else if (lowerMessage.includes('code') || lowerMessage.includes('programming')) {
                response = {
                    response: "I'd love to help you with coding! I can assist with:\n\n• Writing code in Python, JavaScript, HTML/CSS, and many other languages\n• Debugging and fixing errors\n• Code optimization and best practices\n• Algorithm design and data structures\n• Web development and APIs\n\nWhat specific programming task can I help you with?",
                    type: 'code'
                };
            } else if (lowerMessage.includes('write') || lowerMessage.includes('content')) {
                response = {
                    response: "I'm excellent at content creation! I can help you with:\n\n📝 **Writing Services:**\n• Professional emails and letters\n• Articles and blog posts\n• Technical documentation\n• Creative writing\n• Academic papers\n• Marketing copy\n\nWhat type of content would you like me to help you create?",
                    type: 'content'
                };
            } else {
                response = {
                    response: `That's an interesting question! While this is a demo with pre-programmed responses, the real Manus AI can help you with:\n\n• **Research & Analysis** - Finding and analyzing information\n• **Content Creation** - Writing, editing, and formatting\n• **Programming** - Code generation and debugging\n• **Problem Solving** - Breaking down complex challenges\n• **Data Processing** - Analysis and visualization\n\nTry one of the quick prompts above to see more detailed examples!`,
                    type: 'general'
                };
            }
        }

        this.addMessage(response.response, 'ai');
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing-indicator';
        typingDiv.id = 'typing-indicator';

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = '<i class="fas fa-robot"></i>';

        const typingContent = document.createElement('div');
        typingContent.className = 'message-content';
        typingContent.innerHTML = `
            <div class="typing-indicator">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
        `;

        typingDiv.appendChild(avatar);
        typingDiv.appendChild(typingContent);

        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    scrollToBottom() {
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
}

// Initialize demo when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new ManusAIDemo();
});
