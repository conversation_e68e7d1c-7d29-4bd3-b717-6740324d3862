# How to Use Your Manus AI Website

## 🚀 Quick Start

### Option 1: View Locally (Current Setup)
Your website is already running! Simply open your browser and go to:
```
http://localhost:8000
```

### Option 2: Open Directly in Browser
You can also open the `index.html` file directly:
1. Navigate to your project folder: `c:\Users\<USER>\my website\my manus AI`
2. Double-click on `index.html`
3. It will open in your default browser

## 📁 File Structure
```
my manus AI/
├── index.html          # Main website file
├── styles.css          # All the styling
├── script.js           # Interactive features
├── README.md           # Technical documentation
└── USAGE_GUIDE.md      # This guide
```

## 🌐 Deployment Options

### Option A: GitHub Pages (Free)
1. Create a GitHub repository
2. Upload your files to the repository
3. Go to Settings → Pages
4. Select "Deploy from a branch" → main branch
5. Your site will be live at `https://yourusername.github.io/repository-name`

### Option B: Netlify (Free)
1. Go to https://netlify.com
2. Drag and drop your project folder
3. Get instant live URL

### Option C: Vercel (Free)
1. Go to https://vercel.com
2. Import your project
3. Deploy with one click

### Option D: Traditional Web Hosting
Upload all files to any web hosting service via FTP

## ✏️ Customization Guide

### Changing Colors
Edit `styles.css` and look for these color variables:
```css
/* Primary colors */
--primary-blue: #2563eb
--gradient-start: #667eea
--gradient-end: #764ba2
```

### Updating Content
Edit `index.html` to change:
- Text content in any section
- Your contact information
- Add new sections
- Modify the navigation menu

### Adding New Features
Edit `script.js` to add:
- New interactive elements
- Additional animations
- Contact forms
- Analytics tracking

## 📱 Mobile Testing
Test your website on different devices:
1. Desktop browsers (Chrome, Firefox, Safari, Edge)
2. Mobile browsers (resize browser window or use developer tools)
3. Tablet view (medium screen sizes)

## 🔧 Development Setup

### For Live Development
If you want to make changes and see them instantly:

1. **Using Python** (if you have Python installed):
   ```bash
   cd "c:\Users\<USER>\my website\my manus AI"
   python -m http.server 8000
   ```

2. **Using Node.js** (if you have Node.js installed):
   ```bash
   npx http-server
   ```

3. **Using PHP** (if you have PHP installed):
   ```bash
   php -S localhost:8000
   ```

### Making Changes
1. Edit any file (`index.html`, `styles.css`, `script.js`)
2. Save the file
3. Refresh your browser to see changes

## 🎨 Common Customizations

### 1. Change the Logo/Title
In `index.html`, find:
```html
<div class="nav-logo">
    <i class="fas fa-robot"></i>
    <span>Manus AI</span>
</div>
```

### 2. Update Hero Section
In `index.html`, find the hero section and modify:
```html
<h1 class="hero-title">Your Title Here</h1>
<p class="hero-subtitle">Your subtitle here</p>
```

### 3. Add Your Own Content
Replace the capability cards, prompting examples, or about section with your own content.

### 4. Change Fonts
In `styles.css`, modify the font import:
```css
@import url('https://fonts.googleapis.com/css2?family=YourFont:wght@300;400;500;600;700&display=swap');
```

## 📊 Adding Analytics
To track visitors, add Google Analytics or similar:
1. Get your tracking code
2. Add it to the `<head>` section of `index.html`

## 🔒 Adding Contact Forms
To add a working contact form:
1. Use services like Formspree, Netlify Forms, or EmailJS
2. Replace the current buttons with a contact form
3. Add the necessary JavaScript for form handling

## 🚀 Performance Tips
- Images: Optimize any images you add (use WebP format when possible)
- Loading: The site is already optimized for fast loading
- SEO: Add meta descriptions and keywords to `index.html`

## 🆘 Troubleshooting

### Website Not Loading?
- Check if the server is still running
- Try refreshing the browser
- Clear browser cache (Ctrl+F5)

### Styling Looks Wrong?
- Make sure `styles.css` is in the same folder as `index.html`
- Check browser developer tools for errors (F12)

### JavaScript Not Working?
- Make sure `script.js` is in the same folder as `index.html`
- Check browser console for errors (F12 → Console tab)

## 📞 Need Help?
If you need assistance with:
- Customizing the design
- Adding new features
- Deploying to a hosting service
- Technical issues

Just ask! I'm here to help you make this website exactly what you need.
