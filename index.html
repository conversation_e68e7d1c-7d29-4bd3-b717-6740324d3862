<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manus AI Assistant - Intelligent Task Automation</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-robot"></i>
                <span>Manus AI</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#capabilities" class="nav-link">Capabilities</a></li>
                <li><a href="#prompting" class="nav-link">Prompting Guide</a></li>
                <li><a href="#about" class="nav-link">About</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <main>
        <section id="home" class="hero">
            <div class="hero-container">
                <div class="hero-content">
                    <h1 class="hero-title">Manus AI Assistant</h1>
                    <p class="hero-subtitle">Intelligent task automation and problem-solving companion</p>
                    <p class="hero-description">
                        I'm designed to help you with a wide range of tasks using advanced tools and capabilities. 
                        From research and content creation to programming and deployment, I'm here to assist you 
                        in accomplishing your goals efficiently and effectively.
                    </p>
                    <div class="hero-buttons">
                        <a href="#capabilities" class="btn btn-primary">Explore Capabilities</a>
                        <a href="#prompting" class="btn btn-secondary">Learn Prompting</a>
                    </div>
                </div>
                <div class="hero-visual">
                    <div class="ai-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                </div>
            </div>
        </section>

        <section id="capabilities" class="capabilities">
            <div class="container">
                <h2 class="section-title">Capabilities Overview</h2>
                <p class="section-subtitle">
                    Comprehensive AI assistance across multiple domains and technologies
                </p>
                
                <div class="capabilities-grid">
                    <div class="capability-card">
                        <div class="card-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3>Information Processing</h3>
                        <p>Conducting research, fact-checking, data analysis, and information verification from multiple sources with comprehensive summarization capabilities.</p>
                    </div>
                    
                    <div class="capability-card">
                        <div class="card-icon">
                            <i class="fas fa-edit"></i>
                        </div>
                        <h3>Content Creation</h3>
                        <p>Writing articles, reports, documentation, code, and creative content with proper formatting and structure according to specific requirements.</p>
                    </div>
                    
                    <div class="capability-card">
                        <div class="card-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3>Problem Solving</h3>
                        <p>Breaking down complex problems, providing step-by-step solutions, troubleshooting errors, and adapting to changing requirements.</p>
                    </div>
                    
                    <div class="capability-card">
                        <div class="card-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                        <h3>Browser Capabilities</h3>
                        <p>Web navigation, content extraction, element interaction, JavaScript execution, and comprehensive web page analysis.</p>
                    </div>
                    
                    <div class="capability-card">
                        <div class="card-icon">
                            <i class="fas fa-file-code"></i>
                        </div>
                        <h3>File Operations</h3>
                        <p>Reading, writing, organizing files, format conversion, content analysis, and comprehensive file system management.</p>
                    </div>
                    
                    <div class="capability-card">
                        <div class="card-icon">
                            <i class="fas fa-terminal"></i>
                        </div>
                        <h3>Shell & Command Line</h3>
                        <p>Executing commands, software installation, script automation, process management, and system resource manipulation.</p>
                    </div>
                </div>

                <div class="tech-section">
                    <h3>Programming Languages & Technologies</h3>
                    <div class="tech-grid">
                        <div class="tech-category">
                            <h4>Languages</h4>
                            <div class="tech-tags">
                                <span class="tech-tag">JavaScript/TypeScript</span>
                                <span class="tech-tag">Python</span>
                                <span class="tech-tag">HTML/CSS</span>
                                <span class="tech-tag">Shell Scripting</span>
                                <span class="tech-tag">SQL</span>
                                <span class="tech-tag">PHP</span>
                                <span class="tech-tag">Java</span>
                                <span class="tech-tag">C/C++</span>
                                <span class="tech-tag">Go</span>
                            </div>
                        </div>
                        <div class="tech-category">
                            <h4>Frameworks</h4>
                            <div class="tech-tags">
                                <span class="tech-tag">React</span>
                                <span class="tech-tag">Vue</span>
                                <span class="tech-tag">Angular</span>
                                <span class="tech-tag">Node.js</span>
                                <span class="tech-tag">Express</span>
                                <span class="tech-tag">Django</span>
                                <span class="tech-tag">Flask</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="prompting" class="prompting">
            <div class="container">
                <h2 class="section-title">Effective Prompting Guide</h2>
                <p class="section-subtitle">
                    Learn how to craft effective prompts for better AI assistance
                </p>
                
                <div class="prompting-content">
                    <div class="prompting-principles">
                        <h3>Key Principles</h3>
                        <div class="principles-grid">
                            <div class="principle-card">
                                <div class="principle-icon">
                                    <i class="fas fa-bullseye"></i>
                                </div>
                                <h4>Be Specific and Clear</h4>
                                <p>State your request explicitly, include relevant context, specify the format you want, and mention any constraints or requirements.</p>
                            </div>
                            
                            <div class="principle-card">
                                <div class="principle-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <h4>Provide Context</h4>
                                <p>Explain why you need the information, share background knowledge, mention previous attempts, and describe your familiarity level.</p>
                            </div>
                            
                            <div class="principle-card">
                                <div class="principle-icon">
                                    <i class="fas fa-list-ol"></i>
                                </div>
                                <h4>Structure Your Request</h4>
                                <p>Break complex requests into parts, use numbered lists, prioritize information, and organize with headers or sections.</p>
                            </div>
                            
                            <div class="principle-card">
                                <div class="principle-icon">
                                    <i class="fas fa-file-alt"></i>
                                </div>
                                <h4>Specify Output Format</h4>
                                <p>Indicate preferred response length, request specific formats, mention special elements needed, and specify tone and style.</p>
                            </div>
                        </div>
                    </div>

                    <div class="examples-section">
                        <h3>Prompt Examples</h3>
                        <div class="example-comparison">
                            <div class="example-card poor">
                                <h4><i class="fas fa-times-circle"></i> Poor Prompt</h4>
                                <p class="example-text">"Tell me about machine learning."</p>
                            </div>
                            <div class="example-card good">
                                <h4><i class="fas fa-check-circle"></i> Improved Prompt</h4>
                                <p class="example-text">"I'm a computer science student working on my first machine learning project. Could you explain supervised learning algorithms in 2-3 paragraphs, focusing on practical applications in image recognition? Please include 2-3 specific algorithm examples with their strengths and weaknesses."</p>
                            </div>
                        </div>
                    </div>

                    <div class="iterative-process">
                        <h3>Iterative Prompting Process</h3>
                        <div class="process-steps">
                            <div class="step">
                                <div class="step-number">1</div>
                                <div class="step-content">
                                    <h4>Start with Initial Prompt</h4>
                                    <p>Begin with your best attempt at describing what you need</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">2</div>
                                <div class="step-content">
                                    <h4>Review Response</h4>
                                    <p>Evaluate what was helpful and what might be missing</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">3</div>
                                <div class="step-content">
                                    <h4>Refine Your Prompt</h4>
                                    <p>Adjust based on the response to get closer to your goal</p>
                                </div>
                            </div>
                            <div class="step">
                                <div class="step-number">4</div>
                                <div class="step-content">
                                    <h4>Continue Conversation</h4>
                                    <p>Explore the topic further through ongoing dialogue</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="about" class="about">
            <div class="container">
                <h2 class="section-title">About Manus AI Assistant</h2>
                <div class="about-content">
                    <div class="about-intro">
                        <p>I am Manus, an AI assistant designed to help users with a wide variety of tasks. I'm built to be helpful, informative, and versatile in addressing different needs and challenges.</p>
                    </div>
                    
                    <div class="about-grid">
                        <div class="about-card">
                            <h3><i class="fas fa-target"></i> My Purpose</h3>
                            <p>My primary purpose is to assist users in accomplishing their goals by providing information, executing tasks, and offering guidance. I aim to be a reliable partner in problem-solving and task completion.</p>
                        </div>
                        
                        <div class="about-card">
                            <h3><i class="fas fa-route"></i> My Approach</h3>
                            <p>I analyze requests, break down complex problems, use appropriate tools, provide clear communication throughout the process, and deliver organized results.</p>
                        </div>
                        
                        <div class="about-card">
                            <h3><i class="fas fa-heart"></i> My Values</h3>
                            <p>I uphold accuracy and reliability, respect for privacy, ethical technology use, transparency about capabilities, and continuous improvement.</p>
                        </div>
                        
                        <div class="about-card">
                            <h3><i class="fas fa-handshake"></i> Working Together</h3>
                            <p>The most effective collaborations happen when tasks are clearly defined, feedback is provided, requests are specific, and we build on successful interactions.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-robot"></i>
                        <span>Manus AI</span>
                    </div>
                    <p>Intelligent task automation and problem-solving companion</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#capabilities">Capabilities</a></li>
                        <li><a href="#prompting">Prompting Guide</a></li>
                        <li><a href="#about">About</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Manus AI Assistant. Built to assist and empower.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
